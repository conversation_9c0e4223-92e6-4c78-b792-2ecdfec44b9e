#!/bin/bash

# Test script for CNAME resolution functionality
# This script tests the new --resolve-cname option

echo "Testing CNAME resolution functionality..."

# Create a test configuration file
cat > test_dnsmasq.conf << EOF
# Test configuration for CNAME resolution
port=5353
no-daemon
log-queries
resolve-cname
server=*******
cache-size=1000
EOF

echo "Created test configuration file"

# Create a test hosts file with CNAME entries
cat > test_hosts << EOF
# Test hosts file
************* test-server.local
EOF

echo "Created test hosts file"

echo "Configuration created. To test:"
echo "1. Run: sudo ./src/dnsmasq -C test_dnsmasq.conf"
echo "2. In another terminal, test with: dig @127.0.0.1 -p 5353 www.google.com"
echo "3. Check if CNAME chains are resolved automatically"

echo ""
echo "Test configuration file contents:"
cat test_dnsmasq.conf

echo ""
echo "To test manually:"
echo "sudo ./src/dnsmasq -C test_dnsmasq.conf"
